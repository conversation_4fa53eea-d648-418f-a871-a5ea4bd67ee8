using MoreMountains.TopDownEngine;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using MoreMountains.Tools;

public class SIXExtendedWeaponSystem : ExtendedWeaponSystem, MMEventListener<MagicStaffIDChangedEvent>, MMEventListener<OwnedMagicChangedEvent>
{
    [Tooltip("武器模型， 用于换装用")]
    public GameObject WeaponModel;

    // 法杖
    protected MagicStaff _magicStaff;

    public MagicStaff MagicStaff
    {
        get => _magicStaff;
    }

    protected override void Update()
    {
        base.Update();

        UpdateProperties();
        RefreshUI();
    }

    private void RefreshUI()
    {
        if (_magicStaff == null)
        {
            return;
        }

        ScreenBase sb = GameUIManager.GetInstance().GetUI(typeof(MainLeftTopScene));
        if (sb != null)
        {
            MainLeftTopScene leftTopScene = (MainLeftTopScene)sb;
            leftTopScene.UpdateMagic(_magicStaff.CurrentMagicValue, 0, _magicStaff.MaxMagicValue);
        }


        ScreenBase lbsb = GameUIManager.GetInstance().GetUI(typeof(MainLeftBottomScene));
        if (lbsb != null)
        {
            MainLeftBottomScene leftBottomScene = (MainLeftBottomScene)lbsb;
            leftBottomScene.UpdateWeaponItem(_magicStaff);
        }

        // 更新法杖槽位 
        if (lbsb != null)
        {
            ((MainLeftBottomScene)lbsb)?.UpdateStaffSockets(_magicStaff);
        }
    }

    public override void Initialization()
    {
        base.Initialization();

        // 测试数据
        PlayerData.GetInstance().MagicStaffID = 1002;
        if (_magicStaff == null)
        {
            cfg.item.Weapon tbWeapon = DataTableManager.Instance.Tables.TbWeapon.Get((int)PlayerData.GetInstance().MagicStaffID);
            _magicStaff = new MagicStaff(tbWeapon);
            _magicStaff.CurrentMagicValue = _magicStaff.MaxMagicValue;
        }

        if (_enhancedSlots == null || _enhancedSlots.Count == 0)
        {

            List<SlotElement> allElements = PlayerData.GetInstance().OwnedInMagicPack;
            foreach (var e in allElements)
            {
                if (e.SlotType == 1)
                {
                    _enhancedSlots.Add(e);
                }
            }

            _enhancedSlots.Sort((a, b) => a.SlotIndex.CompareTo(b.SlotIndex));
        }

    }

    protected override void UpdateProperties()
    {
        if (_magicStaff == null)
            return;

        _elapsedTime += Time.deltaTime; // Accumulate time

        if (_elapsedTime >= 1f) // Check if a second has passed
        {
            // 回复蓝量
            _magicStaff.CurrentMagicValue += _magicStaff.MagicRegen;
            _magicStaff.CurrentMagicValue = Mathf.Min(_magicStaff.CurrentMagicValue, _magicStaff.MaxMagicValue); // Ensure it doesn't exceed maxMagicValue
            _elapsedTime = 0f; // Reset elapsed time
        }
    }

    protected override void RevertProperties()
    {
       
    }

    public override void WeaponUse()
    {
        ExecuteSkillChain();
    }

    protected override void ExecuteSkillChain()
    {
        // step 1. 检查是否可以释放技能
        if (_magicStaffCastStatus != MagicStaffCastStatus.Ready)
        {// 正在施法中或者冷却中
            return;
        }

        // step 2. 执行技能链
        base.ExecuteSkillChain();

    }

    /// <summary>
    /// 单次施法条件检查
    /// </summary>
    /// <returns></returns>
    protected override bool SingleCastingConditionInspection()
    {
        if (Owner != null)
        {
            if (Owner.CharacterType == MoreMountains.TopDownEngine.Character.CharacterTypes.Player)
            {
                if (!GetSlotElementsAllIndexes().Contains(_currentSkillSlotIndex))
                {
                    _currentSkillSlotIndex = -1;
                    return false;
                }

                SlotElement element = GetSlotElementSlot(_currentSkillSlotIndex);
                if (element.Type == ElementType.Skill)
                { // 当前槽位放置的是技能
                    var skillElement = element as SkillElement;
                    // 从表中获取的获取的蓝耗, 后期可能会考虑叠加上Buff的蓝耗
                    cfg.skill.Spell spell = DataTableManager.Instance.Tables.TbSpell.Get((int)skillElement.ID);
                    // step 1. 检查魔法值是否足够
                    if (_magicStaff.CurrentMagicValue - _magicStaff.MagicConsume - spell.Consume >= 0)
                    {
                        return true;
                    }

                }
            }
        }

        return false;
    }

    /// <summary>
    /// 技能消耗
    /// </summary>
    protected override void HandlIndividualSkillConsumption()
    {
        SlotElement element = GetSlotElementSlot(_currentSkillSlotIndex);
        var skillElement = element as SkillElement;
        // 从表中获取的获取的蓝耗, 后期可能会考虑叠加上Buff的蓝耗
        cfg.skill.Spell spell = DataTableManager.Instance.Tables.TbSpell.Get((int)skillElement.ID);
        _magicStaff.CurrentMagicValue -= _magicStaff.MagicConsume + spell.Consume;
        _magicStaff.CurrentMagicValue = Mathf.Max(0, Mathf.Min(_magicStaff.CurrentMagicValue, _magicStaff.MaxMagicValue));
    }

    /// <summary>
    /// 触发技能链前置条件检查
    /// </summary>
    /// <returns></returns>
    protected override bool CheckIfTheCastingConditionsAreMet()
    {
        if (Owner != null)
        {
            if (Owner.CharacterType == MoreMountains.TopDownEngine.Character.CharacterTypes.Player && _magicStaff != null)
            {
                // step 1. 检查槽位里是否有技能
                if (!HasSkillInSlot())
                {
                    return false;
                }

                // step 2. 检测是否附近有敌人
                if (!ProgrammableWeaponCommonFunctions.FindNearestCharacter(transform.position, _magicStaff.Range))
                {
                    return false;
                }

                return true;
            }
        }

        return false; 
    }

    protected override void DeductingSkillConsumption()
    {
        _magicStaff.CurrentMagicValue -= _magicStaff.MagicConsume; // 法杖的蓝耗
    }

    protected override void refreshViewDisplay()
    {
        ScreenBase sb = GameUIManager.GetInstance().GetUI(typeof(MainLeftTopScene));
        if (sb != null)
        {
            MainLeftTopScene leftTopScene = (MainLeftTopScene)sb;
            leftTopScene.UpdateMagic(_magicStaff.CurrentMagicValue, _magicStaff.MinMagicValue, _magicStaff.MaxMagicValue);
        }

        ScreenBase lbsb = GameUIManager.GetInstance().GetUI(typeof(MainLeftBottomScene));
        if (lbsb != null)
        {
            MainLeftBottomScene leftBottomScene = (MainLeftBottomScene)lbsb;
            leftBottomScene.UpdateWeaponItem(_magicStaff);
        }
    }

    protected override void ResetMagicStaffCastStatus()
    {
        _magicStaffCastStatus = MagicStaffCastStatus.Ready;
    }

    /// <summary>
    /// 施法间隔
    /// </summary>
    /// <returns></returns>
    protected override System.Collections.IEnumerator CastIntervalRoutine()
    {
        _magicStaffCastStatus = MagicStaffCastStatus.OnCastInternal;
        yield return new WaitForSeconds(_magicStaff.CastInterval);
    }

    protected override System.Collections.IEnumerator CooldownRoutine()
    {
        _magicStaffCastStatus = MagicStaffCastStatus.OnCooldown;
        yield return new WaitForSeconds(_magicStaff.Cooldown);
    }

    private List<int> GetSlotElementsAllIndexes()
    {
        List<int> indexes = new List<int>();
        foreach (var slot in _enhancedSlots)
        {
            indexes.Add(slot.SlotIndex);
        }

        return indexes;
    }

    private SlotElement GetSlotElementSlot(int targetSlotIndex)
    {
        foreach (var slot in _enhancedSlots)
        {
            if (slot.SlotIndex == targetSlotIndex)
            {
                return slot;
            }
        }
        return null;
    }

    private bool HasSkillInSlot()
    {
        foreach (var slot in _enhancedSlots)
        {
            if (slot.Type == ElementType.Skill)
            {
                return true;
            }
        }
        return false;
    }

    public void OnMMEvent(MagicStaffIDChangedEvent eventType)
    {
        if (eventType.EventName == "StaffIDChanged")
        {
            // 查数据表， 获取魔法杖数据
            // key:eventType.UintParameter
            cfg.item.Weapon tbWeapon = DataTableManager.Instance.Tables.TbWeapon.Get((int)eventType.UintParameter);
            _magicStaff = new MagicStaff(tbWeapon);
            // test
            _magicStaff.CurrentMagicValue = _magicStaff.MaxMagicValue = 100;

            ScreenBase lbsb = GameUIManager.GetInstance().GetUI(typeof(MainLeftBottomScene));
            if (lbsb != null)
            {
                ((MainLeftBottomScene)lbsb)?.UpdateStaffSockets(_magicStaff);
            }

            // 更新所有槽位，确保UI已正确设置
            // RefreshUI();

            // 法杖总槽位数量（从0开始计数，所以最后一个槽位索引是 SlotNumber-1）
            int lastSlotIndex = (int)_magicStaff.SlotNumber - 1;
            
            // 确保索引在有效范围内
            int availableSlotIndex = lastSlotIndex >= 0 ? lastSlotIndex : 0;
            
            // 创建并完全初始化技能
            var skillProjectile = new SkillProjectile(10001, "法术: 飞弹");
            skillProjectile.Type = ElementType.Skill;
            skillProjectile.SlotType = 1;
            skillProjectile.SlotIndex = availableSlotIndex;
            skillProjectile.ElementDesc = "发射一颗追踪魔法飞弹，对敌人造成伤害";
            skillProjectile.Initialize(); // 确保初始化完成
            
            // 添加到玩家数据
            PlayerData.GetInstance().AddSlotElement(skillProjectile);

            //var skillBlackHole = new SkillBlackHole(10002, "法术：黑洞");
            //skillBlackHole.Type = ElementType.Skill;
            //skillBlackHole.SlotType = 1;
            //skillBlackHole.SlotIndex = 1;
            //skillBlackHole.ElementDesc = "施放一个黑洞，吸引周围敌人";
            //skillBlackHole.Initialize(); // 确保初始化完成

            // 添加到玩家数据
            // PlayerData.GetInstance().AddSlotElement(skillBlackHole);

            // 再次更新UI，确保新添加的技能显示出来
            // RefreshUI();
        }
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        this.MMEventStartListening<MagicStaffIDChangedEvent>();
        this.MMEventStartListening<OwnedMagicChangedEvent>();
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        this.MMEventStopListening<MagicStaffIDChangedEvent>();
        this.MMEventStopListening<OwnedMagicChangedEvent>();
    }

    public void OnMMEvent(OwnedMagicChangedEvent eventType)
    {
        if (eventType.EventName == "MagicPackChanged")
        {
            _enhancedSlots.Clear();
            List<SlotElement> allElements = eventType.ListParameter as List<SlotElement>;
            foreach (var e in allElements)
            {
                if (e.SlotType == 1)
                {
                    _enhancedSlots.Add(e);
                }
            }

            _enhancedSlots.Sort((a, b) => a.SlotIndex.CompareTo(b.SlotIndex));
        }

    }
}
