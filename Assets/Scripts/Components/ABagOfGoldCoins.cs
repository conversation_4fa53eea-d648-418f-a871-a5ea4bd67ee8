using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ABagOfGoldCoins : BaseDropItem
{
    private void Awake()
    {
        // 确保有Collider2D组件用于射线检测
        if (GetComponent<Collider2D>() == null)
        {
            Debug.LogWarning("ABagOfGoldCoins需要Collider2D组件来检测点击！正在添加BoxCollider2D");
            gameObject.AddComponent<BoxCollider2D>();
        }
    }
    
    void Start()
    {
    }
    
    // 处理鼠标点击
    private void OnMouseDown()
    {
        CollectGold();
    }
    
    // 收集金币的方法
    private void CollectGold()
    {
        cfg.item.Item dropItem = DataTableManager.Instance.Tables.TbItems.Get(ItemID);
        if (dropItem.SubItem != null && dropItem.SubItem.Count == 2)
        {
            int itemID = dropItem.SubItem[0];
            int itemCount = dropItem.SubItem[1];

            for (int i = 0; i < itemCount; i++)
            {
                GameObject itemPrefab = Resources.Load<GameObject>($"Items/DropItems/{itemID}");
                if (itemPrefab != null)
                {
                    float maxRadius = 2.8f; // 最大随机范围半径
                    // 在圆形区域内随机生成位置
                    Vector2 randomOffset = Random.insideUnitCircle * maxRadius;
                    Vector3 spawnPosition = transform.position + new Vector3(randomOffset.x, randomOffset.y, 0);

                    // 实例化物品
                    GameObject go = Instantiate(itemPrefab, spawnPosition, Quaternion.identity);
                    go.GetComponent<BaseDropItem>().ItemID = itemID; // 设置掉落物品ID
                }
                else
                {
                    Debug.LogError($"Item prefab not found for ItemId: {itemID}");
                }
            }
        }
        
        // 在这里添加收集金币的逻辑
        // 例如：更新玩家金币数量、播放音效、显示特效等

        // 收集后销毁金币袋
        Destroy(gameObject);
    }
}
